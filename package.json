{"name": "aistudio-automation", "version": "1.0.0", "description": "Browser automation system for Google AI Studio using Playwright", "main": "dist/server.js", "scripts": {"dev": "tsx watch src/server.ts", "build": "tsc && npm run build:frontend", "build:frontend": "cd frontend && npm run build", "start": "node dist/server.js", "test": "jest", "test:e2e": "playwright test", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,json}\"", "migrate": "prisma migrate deploy", "db:generate": "prisma generate", "db:studio": "prisma studio"}, "keywords": ["playwright", "automation", "ai-studio", "google", "browser-automation", "workflow"], "author": "AI Studio Automation Team", "license": "MIT", "dependencies": {"@playwright/test": "^1.40.0", "@prisma/client": "^5.6.0", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "winston": "^3.11.0", "bull": "^4.12.0", "redis": "^4.6.10", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "joi": "^17.11.0", "socket.io": "^4.7.4", "uuid": "^9.0.1", "axios": "^1.6.2", "multer": "^1.4.5-lts.1", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "morgan": "^1.10.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0"}, "devDependencies": {"@types/node": "^20.9.0", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/uuid": "^9.0.7", "@types/multer": "^1.4.11", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@types/jest": "^29.5.8", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "prettier": "^3.1.0", "typescript": "^5.2.2", "tsx": "^4.6.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "prisma": "^5.6.0", "nodemon": "^3.0.1"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}