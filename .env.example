# Environment Configuration
NODE_ENV=development
PORT=3000

# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/aistudio_automation"
REDIS_URL="redis://localhost:6379"

# Authentication
JWT_SECRET="your-super-secure-jwt-secret-change-this-in-production"
JWT_EXPIRES_IN="24h"

# Google OAuth Configuration
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
GOOGLE_REDIRECT_URI="http://localhost:3000/auth/google/callback"

# AI Studio Configuration
AI_STUDIO_BASE_URL="https://aistudio.google.com"
AI_STUDIO_LOGIN_URL="https://accounts.google.com/oauth/authorize"

# Browser Configuration
BROWSER_HEADLESS=true
BROWSER_TIMEOUT=30000
BROWSER_VIEWPORT_WIDTH=1920
BROWSER_VIEWPORT_HEIGHT=1080
MAX_CONCURRENT_BROWSERS=5

# Workflow Configuration
WORKFLOW_TIMEOUT=300000
MAX_WORKFLOW_STEPS=100
WORKFLOW_RETRY_ATTEMPTS=3
WORKFLOW_RETRY_DELAY=5000

# File Upload/Download
UPLOAD_DIR="./uploads"
DOWNLOAD_DIR="./downloads"
MAX_FILE_SIZE=********
ALLOWED_FILE_TYPES="pdf,docx,txt,csv,json"

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_PATH="./logs/app.log"
LOG_MAX_SIZE=********
LOG_MAX_FILES=5

# Security Configuration
CORS_ORIGIN="http://localhost:3000"
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
BCRYPT_SALT_ROUNDS=12

# Monitoring and Analytics
SENTRY_DSN=""
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=9090

# Email Configuration (for notifications)
SMTP_HOST=""
SMTP_PORT=587
SMTP_USER=""
SMTP_PASS=""
SMTP_FROM="<EMAIL>"

# Webhook Configuration
WEBHOOK_SECRET="your-webhook-secret"
WEBHOOK_TIMEOUT=10000

# Development Configuration
DEBUG_MODE=false
MOCK_EXTERNAL_SERVICES=false
ENABLE_API_DOCS=true

# Performance Configuration
CLUSTER_WORKERS=0
MEMORY_LIMIT=1024
CPU_LIMIT=2

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE="0 2 * * *"
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_PATH="./backups"

# Feature Flags
ENABLE_WORKFLOW_TEMPLATES=true
ENABLE_BATCH_EXECUTION=true
ENABLE_REAL_TIME_MONITORING=true
ENABLE_ADVANCED_SCHEDULING=true
