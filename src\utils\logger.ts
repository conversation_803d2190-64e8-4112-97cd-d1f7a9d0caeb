import winston from 'winston';
import path from 'path';

/**
 * Logger configuration and factory
 */
export interface LoggerConfig {
  level: string;
  filePath?: string;
  maxSize?: number;
  maxFiles?: number;
  enableConsole?: boolean;
}

const defaultConfig: LoggerConfig = {
  level: process.env.LOG_LEVEL || 'info',
  filePath: process.env.LOG_FILE_PATH || './logs/app.log',
  maxSize: parseInt(process.env.LOG_MAX_SIZE || '10485760'), // 10MB
  maxFiles: parseInt(process.env.LOG_MAX_FILES || '5'),
  enableConsole: process.env.NODE_ENV !== 'production',
};

/**
 * Create a logger instance with the specified name
 */
export function createLogger(name: string, config: Partial<LoggerConfig> = {}): winston.Logger {
  const finalConfig = { ...defaultConfig, ...config };
  
  const transports: winston.transport[] = [];

  // File transport
  if (finalConfig.filePath) {
    transports.push(
      new winston.transports.File({
        filename: path.resolve(finalConfig.filePath),
        level: finalConfig.level,
        maxsize: finalConfig.maxSize,
        maxFiles: finalConfig.maxFiles,
        tailable: true,
        format: winston.format.combine(
          winston.format.timestamp(),
          winston.format.errors({ stack: true }),
          winston.format.json()
        ),
      })
    );
  }

  // Console transport
  if (finalConfig.enableConsole) {
    transports.push(
      new winston.transports.Console({
        level: finalConfig.level,
        format: winston.format.combine(
          winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
          winston.format.errors({ stack: true }),
          winston.format.colorize(),
          winston.format.printf(({ timestamp, level, message, service, ...meta }) => {
            const metaStr = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
            return `${timestamp} [${service || name}] ${level}: ${message} ${metaStr}`;
          })
        ),
      })
    );
  }

  const logger = winston.createLogger({
    level: finalConfig.level,
    defaultMeta: { service: name },
    transports,
    exitOnError: false,
  });

  // Handle uncaught exceptions and unhandled rejections
  logger.exceptions.handle(
    new winston.transports.File({ 
      filename: path.resolve('./logs/exceptions.log'),
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.json()
      ),
    })
  );

  logger.rejections.handle(
    new winston.transports.File({ 
      filename: path.resolve('./logs/rejections.log'),
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.json()
      ),
    })
  );

  return logger;
}

/**
 * Default application logger
 */
export const logger = createLogger('app');

/**
 * Create a child logger with additional context
 */
export function createChildLogger(
  parent: winston.Logger, 
  context: Record<string, any>
): winston.Logger {
  return parent.child(context);
}

/**
 * Log levels for reference
 */
export const LOG_LEVELS = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  verbose: 4,
  debug: 5,
  silly: 6,
} as const;

/**
 * Utility function to ensure log directory exists
 */
export function ensureLogDirectory(): void {
  const fs = require('fs');
  const logDir = path.dirname(defaultConfig.filePath!);
  
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }
}
