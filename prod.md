# Production Deployment Guide - AI Studio Automation

## Overview

This guide provides comprehensive instructions for deploying the AI Studio Automation system to production environments. It covers infrastructure requirements, deployment procedures, monitoring setup, and maintenance protocols.

## Infrastructure Requirements

### Minimum System Requirements

#### Application Server
- **CPU**: 4 cores (8 recommended)
- **RAM**: 8GB (16GB recommended)
- **Storage**: 100GB SSD (500GB recommended)
- **Network**: 1Gbps connection
- **OS**: Ubuntu 20.04 LTS or CentOS 8+

#### Database Server
- **CPU**: 2 cores (4 recommended)
- **RAM**: 4GB (8GB recommended)
- **Storage**: 50GB SSD with backup storage
- **Network**: 1Gbps connection
- **Database**: PostgreSQL 13+ with Redis 6+

#### Browser Automation Nodes
- **CPU**: 2 cores per concurrent browser
- **RAM**: 2GB per concurrent browser
- **Storage**: 20GB per node
- **Display**: Xvfb for headless operation
- **Browsers**: Chromium, Firefox, Safari (macOS only)

### Recommended Production Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                        Load Balancer                           │
│                     (NGINX/HAProxy)                            │
└─────────────────────┬───────────────────────────────────────────┘
                      │
    ┌─────────────────┼─────────────────┐
    │                 │                 │
┌───▼───┐         ┌───▼───┐         ┌───▼───┐
│ App   │         │ App   │         │ App   │
│ Node  │         │ Node  │         │ Node  │
│   1   │         │   2   │         │   3   │
└───────┘         └───────┘         └───────┘
    │                 │                 │
    └─────────────────┼─────────────────┘
                      │
┌─────────────────────▼─────────────────────┐
│              Database Cluster             │
│  ┌─────────────┐    ┌─────────────────┐   │
│  │ PostgreSQL  │    │ Redis Cluster   │   │
│  │ Primary +   │    │ (Cache + Queue) │   │
│  │ Replica     │    │                 │   │
│  └─────────────┘    └─────────────────────┘   │
└─────────────────────────────────────────────┘
```

## Pre-Deployment Setup

### 1. Environment Preparation

#### System Dependencies
```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install Node.js 18+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install Docker and Docker Compose
sudo apt-get install -y docker.io docker-compose

# Install system dependencies for browsers
sudo apt-get install -y \
  libnss3-dev \
  libatk-bridge2.0-dev \
  libdrm2 \
  libxkbcommon-dev \
  libxcomposite-dev \
  libxdamage-dev \
  libxrandr2 \
  libgbm-dev \
  libxss1 \
  libasound2-dev
```

#### User and Permissions Setup
```bash
# Create application user
sudo useradd -m -s /bin/bash aistudio-automation
sudo usermod -aG docker aistudio-automation

# Create application directories
sudo mkdir -p /opt/aistudio-automation/{app,data,logs,backups}
sudo chown -R aistudio-automation:aistudio-automation /opt/aistudio-automation
```

### 2. Database Setup

#### PostgreSQL Configuration
```sql
-- Create database and user
CREATE DATABASE aistudio_automation;
CREATE USER aistudio_user WITH ENCRYPTED PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE aistudio_automation TO aistudio_user;

-- Configure connection limits
ALTER USER aistudio_user CONNECTION LIMIT 50;
```

#### Redis Configuration
```bash
# Redis configuration for production
sudo tee /etc/redis/redis.conf << EOF
bind 127.0.0.1
port 6379
timeout 0
tcp-keepalive 300
daemonize yes
supervised systemd
pidfile /var/run/redis/redis-server.pid
loglevel notice
logfile /var/log/redis/redis-server.log
databases 16
save 900 1
save 300 10
save 60 10000
maxmemory 2gb
maxmemory-policy allkeys-lru
EOF
```

### 3. SSL Certificate Setup

#### Using Let's Encrypt
```bash
# Install Certbot
sudo apt-get install -y certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# Set up automatic renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## Deployment Procedures

### 1. Application Deployment

#### Using Docker Compose
```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.prod
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=******************************/aistudio_automation
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=${JWT_SECRET}
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    depends_on:
      - db
      - redis
    restart: unless-stopped

  db:
    image: postgres:13
    environment:
      - POSTGRES_DB=aistudio_automation
      - POSTGRES_USER=aistudio_user
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
    restart: unless-stopped

  redis:
    image: redis:6-alpine
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
```

#### Deployment Script
```bash
#!/bin/bash
# deploy.sh

set -e

echo "Starting deployment..."

# Pull latest code
git pull origin main

# Build application
npm ci --production
npm run build

# Run database migrations
npm run migrate

# Update Docker containers
docker-compose -f docker-compose.prod.yml pull
docker-compose -f docker-compose.prod.yml up -d --build

# Health check
sleep 30
curl -f http://localhost:3000/health || exit 1

echo "Deployment completed successfully!"
```

### 2. NGINX Configuration

```nginx
# nginx.conf
events {
    worker_connections 1024;
}

http {
    upstream app_servers {
        server app:3000;
        # Add more servers for load balancing
        # server app2:3000;
        # server app3:3000;
    }

    server {
        listen 80;
        server_name your-domain.com www.your-domain.com;
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name your-domain.com www.your-domain.com;

        ssl_certificate /etc/nginx/ssl/fullchain.pem;
        ssl_certificate_key /etc/nginx/ssl/privkey.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";

        # Rate limiting
        limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
        limit_req zone=api burst=20 nodelay;

        location / {
            proxy_pass http://app_servers;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
            
            # Timeouts
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }

        # WebSocket support
        location /ws {
            proxy_pass http://app_servers;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Static files
        location /static {
            alias /var/www/static;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
}
```

## Monitoring and Logging

### 1. Application Monitoring

#### Health Check Endpoint
```javascript
// health.js
app.get('/health', (req, res) => {
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    database: 'connected', // Check DB connection
    redis: 'connected',    // Check Redis connection
    browsers: 'available'  // Check browser availability
  };
  
  res.status(200).json(health);
});
```

#### Prometheus Metrics
```javascript
// metrics.js
const prometheus = require('prom-client');

const httpRequestDuration = new prometheus.Histogram({
  name: 'http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status']
});

const workflowExecutions = new prometheus.Counter({
  name: 'workflow_executions_total',
  help: 'Total number of workflow executions',
  labelNames: ['status', 'workflow_type']
});

const browserSessions = new prometheus.Gauge({
  name: 'browser_sessions_active',
  help: 'Number of active browser sessions'
});
```

### 2. Log Management

#### Structured Logging Configuration
```javascript
// logger.js
const winston = require('winston');

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'aistudio-automation' },
  transports: [
    new winston.transports.File({ 
      filename: '/app/logs/error.log', 
      level: 'error' 
    }),
    new winston.transports.File({ 
      filename: '/app/logs/combined.log' 
    }),
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ]
});
```

#### Log Rotation Setup
```bash
# /etc/logrotate.d/aistudio-automation
/opt/aistudio-automation/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 aistudio-automation aistudio-automation
    postrotate
        docker-compose -f /opt/aistudio-automation/docker-compose.prod.yml restart app
    endscript
}
```

## Security Configuration

### 1. Environment Variables
```bash
# .env.production
NODE_ENV=production
PORT=3000

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/aistudio_automation
REDIS_URL=redis://localhost:6379

# Authentication
JWT_SECRET=your-super-secure-jwt-secret
JWT_EXPIRES_IN=24h
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Security
CORS_ORIGIN=https://your-domain.com
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Monitoring
SENTRY_DSN=your-sentry-dsn
LOG_LEVEL=info
```

### 2. Firewall Configuration
```bash
# UFW firewall setup
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

## Backup and Recovery

### 1. Database Backup
```bash
#!/bin/bash
# backup-db.sh

BACKUP_DIR="/opt/aistudio-automation/backups"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
DB_NAME="aistudio_automation"

# Create PostgreSQL backup
pg_dump -h localhost -U aistudio_user -d $DB_NAME | gzip > "$BACKUP_DIR/db_backup_$TIMESTAMP.sql.gz"

# Create Redis backup
redis-cli --rdb "$BACKUP_DIR/redis_backup_$TIMESTAMP.rdb"

# Clean old backups (keep 30 days)
find $BACKUP_DIR -name "*.gz" -mtime +30 -delete
find $BACKUP_DIR -name "*.rdb" -mtime +30 -delete

echo "Backup completed: $TIMESTAMP"
```

### 2. Automated Backup Schedule
```bash
# Add to crontab
0 2 * * * /opt/aistudio-automation/scripts/backup-db.sh
```

## Performance Optimization

### 1. Application Tuning
```javascript
// pm2.config.js
module.exports = {
  apps: [{
    name: 'aistudio-automation',
    script: './dist/server.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024'
  }]
};
```

### 2. Database Optimization
```sql
-- PostgreSQL performance tuning
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET default_statistics_target = 100;
SELECT pg_reload_conf();
```

## Troubleshooting

### Common Issues and Solutions

#### High Memory Usage
- Monitor browser instances and implement cleanup
- Adjust browser pool size based on available memory
- Enable memory limits in Docker containers

#### Database Connection Issues
- Check connection pool settings
- Monitor active connections
- Implement connection retry logic

#### Browser Automation Failures
- Verify browser dependencies are installed
- Check display server (Xvfb) status
- Monitor browser process limits

### Emergency Procedures

#### Service Recovery
```bash
# Quick service restart
docker-compose -f docker-compose.prod.yml restart

# Full system recovery
docker-compose -f docker-compose.prod.yml down
docker-compose -f docker-compose.prod.yml up -d

# Database recovery from backup
gunzip -c /opt/aistudio-automation/backups/db_backup_latest.sql.gz | psql -h localhost -U aistudio_user -d aistudio_automation
```

## Maintenance Schedule

### Daily Tasks
- Monitor system health and performance
- Check error logs for issues
- Verify backup completion

### Weekly Tasks
- Review security logs
- Update system packages
- Performance analysis and optimization

### Monthly Tasks
- Security audit and updates
- Capacity planning review
- Disaster recovery testing
