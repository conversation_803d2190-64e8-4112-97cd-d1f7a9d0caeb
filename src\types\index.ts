/**
 * Core type definitions for AI Studio Automation
 */

export interface User {
  id: string;
  email: string;
  name: string;
  googleId?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Workflow {
  id: string;
  name: string;
  description: string;
  userId: string;
  steps: WorkflowStep[];
  configuration: WorkflowConfiguration;
  status: WorkflowStatus;
  createdAt: Date;
  updatedAt: Date;
  lastExecutedAt?: Date;
}

export interface WorkflowStep {
  id: string;
  type: ActionType;
  order: number;
  configuration: ActionConfiguration;
  conditions?: StepCondition[];
  retryConfig?: RetryConfiguration;
}

export interface WorkflowConfiguration {
  timeout: number;
  retryAttempts: number;
  retryDelay: number;
  browserConfig: BrowserConfiguration;
  notifications: NotificationConfiguration;
}

export interface BrowserConfiguration {
  headless: boolean;
  viewport: { width: number; height: number };
  userAgent?: string;
  locale?: string;
  timezone?: string;
  permissions?: string[];
  recordVideo?: boolean;
  recordTrace?: boolean;
}

export interface NotificationConfiguration {
  onSuccess: boolean;
  onFailure: boolean;
  onStart: boolean;
  channels: NotificationChannel[];
}

export interface NotificationChannel {
  type: 'email' | 'webhook' | 'slack';
  configuration: Record<string, any>;
}

export interface WorkflowExecution {
  id: string;
  workflowId: string;
  userId: string;
  status: ExecutionStatus;
  startedAt: Date;
  completedAt?: Date;
  error?: string;
  results: ExecutionResult[];
  logs: ExecutionLog[];
  metrics: ExecutionMetrics;
}

export interface ExecutionResult {
  stepId: string;
  success: boolean;
  data?: any;
  error?: string;
  screenshots?: string[];
  duration: number;
}

export interface ExecutionLog {
  id: string;
  level: LogLevel;
  message: string;
  timestamp: Date;
  stepId?: string;
  metadata?: Record<string, any>;
}

export interface ExecutionMetrics {
  totalDuration: number;
  stepsCompleted: number;
  stepsTotal: number;
  memoryUsage: number;
  cpuUsage: number;
  networkRequests: number;
}

export type WorkflowStatus = 'draft' | 'active' | 'paused' | 'archived';
export type ExecutionStatus = 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

export type ActionType = 
  | 'navigate'
  | 'click'
  | 'type'
  | 'select'
  | 'wait'
  | 'scroll'
  | 'screenshot'
  | 'extract'
  | 'upload'
  | 'download'
  | 'custom';

export interface ActionConfiguration {
  [key: string]: any;
}

export interface NavigateAction extends ActionConfiguration {
  url: string;
  waitUntil?: 'load' | 'domcontentloaded' | 'networkidle';
}

export interface ClickAction extends ActionConfiguration {
  selector: string;
  button?: 'left' | 'right' | 'middle';
  clickCount?: number;
  force?: boolean;
  position?: { x: number; y: number };
}

export interface TypeAction extends ActionConfiguration {
  selector: string;
  text: string;
  delay?: number;
  clear?: boolean;
}

export interface SelectAction extends ActionConfiguration {
  selector: string;
  value?: string;
  label?: string;
  index?: number;
}

export interface WaitAction extends ActionConfiguration {
  type: 'timeout' | 'selector' | 'function' | 'load';
  value: string | number;
  timeout?: number;
}

export interface ScrollAction extends ActionConfiguration {
  selector?: string;
  x?: number;
  y?: number;
  behavior?: 'auto' | 'smooth';
}

export interface ScreenshotAction extends ActionConfiguration {
  selector?: string;
  fullPage?: boolean;
  path?: string;
  quality?: number;
}

export interface ExtractAction extends ActionConfiguration {
  selector: string;
  attribute?: string;
  multiple?: boolean;
  transform?: string;
}

export interface UploadAction extends ActionConfiguration {
  selector: string;
  files: string[];
}

export interface DownloadAction extends ActionConfiguration {
  selector: string;
  path?: string;
  timeout?: number;
}

export interface CustomAction extends ActionConfiguration {
  script: string;
  args?: any[];
}

export interface StepCondition {
  type: 'element_exists' | 'element_visible' | 'text_contains' | 'url_matches' | 'custom';
  selector?: string;
  value?: string;
  script?: string;
}

export interface RetryConfiguration {
  attempts: number;
  delay: number;
  backoff?: 'linear' | 'exponential';
  conditions?: string[];
}

export interface BrowserSession {
  id: string;
  userId: string;
  browserType: 'chromium' | 'firefox' | 'webkit';
  context: any; // Playwright BrowserContext
  pages: BrowserPage[];
  createdAt: Date;
  lastUsedAt: Date;
  isActive: boolean;
}

export interface BrowserPage {
  id: string;
  sessionId: string;
  page: any; // Playwright Page
  url: string;
  title: string;
  createdAt: Date;
  lastUsedAt: Date;
}

export interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  tags: string[];
  workflow: Omit<Workflow, 'id' | 'userId' | 'createdAt' | 'updatedAt'>;
  isPublic: boolean;
  createdBy: string;
  usageCount: number;
  rating: number;
}

export interface SystemConfiguration {
  maxConcurrentBrowsers: number;
  defaultTimeout: number;
  maxWorkflowSteps: number;
  allowedDomains: string[];
  rateLimits: RateLimitConfiguration;
  security: SecurityConfiguration;
}

export interface RateLimitConfiguration {
  windowMs: number;
  maxRequests: number;
  skipSuccessfulRequests: boolean;
  skipFailedRequests: boolean;
}

export interface SecurityConfiguration {
  allowedOrigins: string[];
  csrfProtection: boolean;
  contentSecurityPolicy: boolean;
  rateLimiting: boolean;
}

export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: string;
}

export interface PaginatedResponse<T> extends APIResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface WebSocketMessage {
  type: string;
  payload: any;
  timestamp: string;
  executionId?: string;
  userId?: string;
}

export interface HealthCheck {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  uptime: number;
  memory: NodeJS.MemoryUsage;
  database: 'connected' | 'disconnected' | 'error';
  redis: 'connected' | 'disconnected' | 'error';
  browsers: 'available' | 'unavailable' | 'limited';
  version: string;
}
