import { <PERSON><PERSON><PERSON>, <PERSON>rowser<PERSON>ontex<PERSON>, Page, chromium, firefox, webkit } from 'playwright';
import { BrowserConfiguration, BrowserSession, BrowserPage } from '@/types';
import { createLogger } from '@/utils/logger';

type Logger = ReturnType<typeof createLogger>;
import { EventEmitter } from 'events';

/**
 * Manages browser instances and sessions for AI Studio automation
 */
export class BrowserManager extends EventEmitter {
  private browsers: Map<string, Browser> = new Map();
  private sessions: Map<string, BrowserSession> = new Map();
  private logger: Logger;
  private maxConcurrentBrowsers: number;
  private defaultConfig: BrowserConfiguration;

  constructor(
    maxConcurrentBrowsers: number = 5,
    defaultConfig: BrowserConfiguration = {
      headless: true,
      viewport: { width: 1920, height: 1080 },
      permissions: ['downloads'],
    }
  ) {
    super();
    this.maxConcurrentBrowsers = maxConcurrentBrowsers;
    this.defaultConfig = defaultConfig;
    this.logger = createLogger('BrowserManager');
    
    // Cleanup inactive sessions periodically
    setInterval(() => this.cleanupInactiveSessions(), 300000); // 5 minutes
  }

  /**
   * Create a new browser session
   */
  async createSession(
    userId: string,
    browserType: 'chromium' | 'firefox' | 'webkit' = 'chromium',
    config?: Partial<BrowserConfiguration>
  ): Promise<BrowserSession> {
    if (this.sessions.size >= this.maxConcurrentBrowsers) {
      throw new Error('Maximum concurrent browsers limit reached');
    }

    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const finalConfig = { ...this.defaultConfig, ...config };

    try {
      const browser = await this.getBrowser(browserType);
      const context = await this.createBrowserContext(browser, finalConfig);
      
      const session: BrowserSession = {
        id: sessionId,
        userId,
        browserType,
        context,
        pages: [],
        createdAt: new Date(),
        lastUsedAt: new Date(),
        isActive: true,
      };

      this.sessions.set(sessionId, session);
      
      this.logger.info(`Created browser session ${sessionId} for user ${userId}`, {
        sessionId,
        userId,
        browserType,
        totalSessions: this.sessions.size,
      });

      this.emit('sessionCreated', session);
      return session;
    } catch (error) {
      this.logger.error(`Failed to create browser session: ${error}`, {
        userId,
        browserType,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  /**
   * Get or create a browser instance
   */
  private async getBrowser(browserType: 'chromium' | 'firefox' | 'webkit'): Promise<Browser> {
    if (this.browsers.has(browserType)) {
      const browser = this.browsers.get(browserType)!;
      if (browser.isConnected()) {
        return browser;
      } else {
        this.browsers.delete(browserType);
      }
    }

    let browser: Browser;
    const launchOptions = {
      headless: this.defaultConfig.headless,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu',
      ],
    };

    switch (browserType) {
      case 'chromium':
        browser = await chromium.launch(launchOptions);
        break;
      case 'firefox':
        browser = await firefox.launch(launchOptions);
        break;
      case 'webkit':
        browser = await webkit.launch(launchOptions);
        break;
      default:
        throw new Error(`Unsupported browser type: ${browserType}`);
    }

    this.browsers.set(browserType, browser);
    
    browser.on('disconnected', () => {
      this.logger.warn(`Browser ${browserType} disconnected`);
      this.browsers.delete(browserType);
    });

    this.logger.info(`Launched ${browserType} browser`);
    return browser;
  }

  /**
   * Create a browser context with configuration
   */
  private async createBrowserContext(
    browser: Browser,
    config: BrowserConfiguration
  ): Promise<BrowserContext> {
    const contextOptions: any = {
      viewport: config.viewport,
      userAgent: config.userAgent,
      locale: config.locale,
      timezoneId: config.timezone,
      permissions: config.permissions,
      recordVideo: config.recordVideo ? { dir: './recordings' } : undefined,
      recordHar: config.recordTrace ? { path: './traces' } : undefined,
    };

    const context = await browser.newContext(contextOptions);
    
    // Set up context event listeners
    context.on('page', (page) => {
      this.logger.debug('New page created in context');
    });

    context.on('close', () => {
      this.logger.debug('Browser context closed');
    });

    return context;
  }

  /**
   * Create a new page in a session
   */
  async createPage(sessionId: string): Promise<BrowserPage> {
    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new Error(`Session ${sessionId} not found`);
    }

    if (!session.isActive) {
      throw new Error(`Session ${sessionId} is not active`);
    }

    try {
      const page = await session.context.newPage();
      const pageId = `page_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      const browserPage: BrowserPage = {
        id: pageId,
        sessionId,
        page,
        url: 'about:blank',
        title: '',
        createdAt: new Date(),
        lastUsedAt: new Date(),
      };

      // Set up page event listeners
      page.on('load', () => {
        browserPage.url = page.url();
        browserPage.title = page.title() || '';
        browserPage.lastUsedAt = new Date();
      });

      page.on('close', () => {
        const index = session.pages.findIndex(p => p.id === pageId);
        if (index > -1) {
          session.pages.splice(index, 1);
        }
      });

      session.pages.push(browserPage);
      session.lastUsedAt = new Date();

      this.logger.debug(`Created page ${pageId} in session ${sessionId}`);
      this.emit('pageCreated', browserPage);
      
      return browserPage;
    } catch (error) {
      this.logger.error(`Failed to create page in session ${sessionId}: ${error}`);
      throw error;
    }
  }

  /**
   * Get a session by ID
   */
  getSession(sessionId: string): BrowserSession | undefined {
    return this.sessions.get(sessionId);
  }

  /**
   * Get all sessions for a user
   */
  getUserSessions(userId: string): BrowserSession[] {
    return Array.from(this.sessions.values()).filter(session => session.userId === userId);
  }

  /**
   * Close a specific session
   */
  async closeSession(sessionId: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (!session) {
      this.logger.warn(`Attempted to close non-existent session ${sessionId}`);
      return;
    }

    try {
      await session.context.close();
      session.isActive = false;
      this.sessions.delete(sessionId);
      
      this.logger.info(`Closed session ${sessionId}`, {
        sessionId,
        userId: session.userId,
        pagesCount: session.pages.length,
      });

      this.emit('sessionClosed', session);
    } catch (error) {
      this.logger.error(`Failed to close session ${sessionId}: ${error}`);
      throw error;
    }
  }

  /**
   * Close all sessions for a user
   */
  async closeUserSessions(userId: string): Promise<void> {
    const userSessions = this.getUserSessions(userId);
    
    await Promise.all(
      userSessions.map(session => this.closeSession(session.id))
    );

    this.logger.info(`Closed all sessions for user ${userId}`, {
      userId,
      sessionCount: userSessions.length,
    });
  }

  /**
   * Clean up inactive sessions
   */
  private async cleanupInactiveSessions(): Promise<void> {
    const now = new Date();
    const inactiveThreshold = 30 * 60 * 1000; // 30 minutes
    const sessionsToClose: string[] = [];

    for (const [sessionId, session] of this.sessions) {
      const inactiveTime = now.getTime() - session.lastUsedAt.getTime();
      if (inactiveTime > inactiveThreshold) {
        sessionsToClose.push(sessionId);
      }
    }

    if (sessionsToClose.length > 0) {
      this.logger.info(`Cleaning up ${sessionsToClose.length} inactive sessions`);
      
      await Promise.all(
        sessionsToClose.map(sessionId => this.closeSession(sessionId))
      );
    }
  }

  /**
   * Get system statistics
   */
  getStats(): {
    totalSessions: number;
    activeSessions: number;
    totalPages: number;
    browserTypes: Record<string, number>;
  } {
    const activeSessions = Array.from(this.sessions.values()).filter(s => s.isActive);
    const totalPages = activeSessions.reduce((sum, session) => sum + session.pages.length, 0);
    
    const browserTypes: Record<string, number> = {};
    activeSessions.forEach(session => {
      browserTypes[session.browserType] = (browserTypes[session.browserType] || 0) + 1;
    });

    return {
      totalSessions: this.sessions.size,
      activeSessions: activeSessions.length,
      totalPages,
      browserTypes,
    };
  }

  /**
   * Shutdown all browsers and sessions
   */
  async shutdown(): Promise<void> {
    this.logger.info('Shutting down browser manager');

    // Close all sessions
    const sessionIds = Array.from(this.sessions.keys());
    await Promise.all(sessionIds.map(id => this.closeSession(id)));

    // Close all browsers
    const browsers = Array.from(this.browsers.values());
    await Promise.all(browsers.map(browser => browser.close()));

    this.browsers.clear();
    this.sessions.clear();

    this.logger.info('Browser manager shutdown complete');
  }
}
